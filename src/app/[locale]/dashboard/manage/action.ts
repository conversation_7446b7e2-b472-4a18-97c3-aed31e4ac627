"use server";

import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { getSubcategories, productFormSchema } from "./utils";
import { Role } from "@prisma/client";
import { revalidatePath, revalidateTag } from "next/cache";
import { <PERSON><PERSON>Key } from "@udoy/utils/cache-key";
import { FileManager } from "@udoy/libs/backend/image-util";
import { createSmallId } from "@udoy/utils/cuid";

export async function updateProduct(productId: string, data: any) {
  try {
    const userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const values = productFormSchema.parse(data);

    const product = await prisma.product.update({
      where: { id: productId },
      data: values,
      include: {
        category: true,
        unit: true,
        images: true,
      },
    });

    if (product) {
      revalidatePath(`/${product.category?.slug}`);
      revalidatePath(`/dashboard/shop/${product.category?.slug}`);
      return product;
    }

    return null;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Update Product");
  }
}

export async function hideCategory(categoryId: string, hide: boolean) {
  try {
    const userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const category = await prisma.category.findUnique({
      where: { id: categoryId },
      // select: {
      //   id: true,
      //   isBase: true,
      //   slug: true,
      //   parentCategory: {
      //     select: {
      //       slug: true,
      //     },
      //   },
      //   subCategories: {
      //     select: {
      //       id: true,
      //       isBase: true,
      //       subCategories: {
      //         select: {
      //           isBase: true,
      //           id: true,
      //         },
      //       },
      //     },
      //   },
      // },
    });

    if (!category) {
      return ActionError("Invalid Category");
    }

    // const { subIds, baseIds } = getSubcategories(category as any);

    await prisma.category.update({
      where: { id: categoryId },
      data: { hide },
    });

    // await prisma.product.updateMany({
    //   where: { categoryId: { in: baseIds } },
    //   data: { hide },
    // });

    if (category) {
      revalidatePath(`/dashboard/manage/${category.slug}`);
      revalidateTag(CacheKey.ShopPage());
      revalidateTag(CacheKey.SidebarCategory());
      revalidateTag(CacheKey.MostPopularCategories());
      revalidateTag(CacheKey.MostPopularProducts());
      return true;
    }

    return false;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Hide Category");
  }
}

export async function updateCategory(categoryId: string, data: FormData | any) {
  try {
    const userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Handle FormData (from enhanced dialog) or regular object (from old dialog)
    let updateData: any;
    let imageFile: File | null = null;

    if (data instanceof FormData) {
      const parentIdValue = data.get("parentId") as string;
      updateData = {
        name: data.get("name") as string,
        nam: data.get("nam") as string || null,
        slug: data.get("slug") as string,
        isBase: data.get("isBase") === "true",
        featured: data.get("featured") === "true",
        position: parseInt(data.get("position") as string) || 0,
        parentId: parentIdValue === "none" || !parentIdValue ? null : parentIdValue,
      };
      imageFile = data.get("image") as File;
    } else {
      updateData = {
        name: data.name,
        nam: data.nam,
        slug: data.slug,
        isBase: data.isBase,
        featured: data.featured,
        position: data.position,
        parentId: data.parentId || null,
      };
    }

    // Validate parent category if provided
    if (updateData.parentId) {
      const parentCategory = await prisma.category.findUnique({
        where: { id: updateData.parentId },
      });

      if (!parentCategory) {
        return ActionError("Invalid parent category");
      }

      if (parentCategory.isBase) {
        return ActionError("Cannot set base category as parent");
      }

      if (parentCategory.id === categoryId) {
        return ActionError("Category cannot be its own parent");
      }
    }

    // Handle image upload if provided
    if (imageFile && imageFile.size > 0) {
      const currentCategory = await prisma.category.findUnique({
        where: { id: categoryId },
        select: { image: true },
      });

      // Remove old image if exists
      if (currentCategory?.image) {
        try {
          await FileManager.imageRemove(currentCategory.image);
        } catch (error) {
          console.log("Failed to remove old image:", error);
        }
      }

      // Upload new image
      const imageId = createSmallId();
      const imageUrl = await FileManager.imageUpload(imageFile, `categories/${imageId}`);
      updateData.image = imageUrl;
    }

    const category = await prisma.category.update({
      where: { id: categoryId },
      data: updateData,
      include: {
        parentCategory: {
          select: {
            slug: true,
          },
        },
      },
    });

    if (category) {
      revalidatePath(`/dashboard/manage/${category.slug}`);
      revalidateTag(CacheKey.SidebarCategory());
      revalidatePath(`/${category.slug}`);
      revalidatePath(`/${category.parentCategory?.slug}`);
      revalidateTag(CacheKey.MostPopularCategories());
      return category;
    }

    return false;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Update Category");
  }
}

export async function createCategory(data: FormData) {
  try {
    const userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const parentIdValue = data.get("parentId") as string;
    const categoryData = {
      name: data.get("name") as string,
      nam: data.get("nam") as string || null,
      slug: data.get("slug") as string,
      isBase: data.get("isBase") === "true",
      featured: data.get("featured") === "true",
      position: parseInt(data.get("position") as string) || 0,
      parentId: parentIdValue === "none" || !parentIdValue ? null : parentIdValue,
    };

    const imageFile = data.get("image") as File;

    // Validate required fields
    if (!categoryData.name || !categoryData.slug) {
      return ActionError("Name and slug are required");
    }

    // Check if slug already exists
    const existingCategory = await prisma.category.findUnique({
      where: { slug: categoryData.slug },
    });

    if (existingCategory) {
      return ActionError("Category with this slug already exists");
    }

    // Validate parent category if provided
    if (categoryData.parentId) {
      const parentCategory = await prisma.category.findUnique({
        where: { id: categoryData.parentId },
      });

      if (!parentCategory) {
        return ActionError("Invalid parent category");
      }

      if (parentCategory.isBase) {
        return ActionError("Cannot set base category as parent");
      }
    }

    // Create category first
    const category = await prisma.category.create({
      data: categoryData,
      include: {
        parentCategory: {
          select: {
            slug: true,
          },
        },
      },
    });

    // Handle image upload if provided
    if (imageFile && imageFile.size > 0) {
      const imageId = createSmallId();
      const imageUrl = await FileManager.imageUpload(imageFile, `categories/${imageId}`);

      await prisma.category.update({
        where: { id: category.id },
        data: { image: imageUrl },
      });
    }

    if (category) {
      revalidatePath(`/dashboard/manage`);
      revalidatePath(`/dashboard/manage/${category.slug}`);
      revalidateTag(CacheKey.SidebarCategory());
      revalidatePath(`/${category.slug}`);
      revalidatePath(`/${category.parentCategory?.slug}`);
      revalidateTag(CacheKey.MostPopularCategories());
      return category;
    }

    return false;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Create Category");
  }
}
